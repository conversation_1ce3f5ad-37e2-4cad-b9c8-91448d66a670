`timescale 1ns / 1ps

//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    22:41:30 09/26/2011 
// Design Name: 
// Module Name:    rtc 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module rtc(clk50m, reset, csec, sec, min, hour, day, week, month, yearh, yearl );
input clk50m, reset;
//output tick10ms;
output [7:0] yearh, yearl;
output [7:0] csec, sec, min, hour, day, week, month;

reg [3:0] y3, y2, y1, y0;
reg [3:0] csech, csecl, sech, secl, minh, minl, hourh, hourl, dayh, dayl, monh, monl;
reg [7:0] week;
wire tick10ms;
wire isleap;
//reg isleap = 1;

always @(posedge clk50m)
if (reset)		//reset to some initial test value
	begin
		{y3, y2, y1, y0} <= 'h2012;
		{monh, monl} <= 'h02;
		{dayh, dayl} <= 'h29;
		{hourh, hourl} <= 'h23;
		{minh, minl} <= 'h59;
		{sech, secl} <= 'h59;
		week <= 7;
		{csech, csecl} <= 'h99;

	end
else if (tick10ms == 1)
	begin
		csecl <= csecl + 1;
		if (csecl == 9)
		begin
			csecl <= 0;
			csech <= csech + 1;
			if ({csech,csecl} >= 8'h99)
			begin
				{csech,csecl} <= 0;
				secl <= secl +1;
				if (secl == 9)
				begin
					secl <= 0;
					sech <= sech + 1;
					if ({sech,secl} >= 8'h59)
					begin
						{sech,secl} <= 0;
						minl <= minl + 1;
						if (minl == 9)
						begin
							minl <= 0;
							minh <= minh + 1;
							if ({minh,minl} >= 8'h59)
							begin
								{minh,minl} <= 0;
								hourl <= hourl +1;
								if ((hourl == 9) && ((hourh == 0) || (hourh == 1)))
								begin
									hourl <= 0;
									hourh <= hourh + 1;
								end
								if ({hourh,hourl} >= 8'h23)
								begin
									{hourh,hourl} <= 0;
									week <= week + 1;
									if (week >= 7)
										week <= 1;
									dayl <= dayl + 1;
									if (dayl == 9)
									begin
										dayl <= 0;
										dayh <= dayh + 1;
									end
									
									if ((( ({monh, monl}==1) || ({monh, monl}==3) || ({monh, monl}==5) || ({monh, monl}==7) || ({monh, monl}==8) || ({monh, monl}==8'h10) || ({monh, monl}==8'h12) ) && ({dayh,dayl} >= 8'h31) ) || (( ({monh, monl}==4) || ({monh, monl}==6) || ({monh, monl}==9) || ({monh, monl}==8'h11) ) && ({dayh,dayl} >= 8'h30) ) || ( ({monh, monl}==2) && isleap && ({dayh,dayl} >= 8'h29) ) || ( ({monh, monl}==2) && (!isleap) && ({dayh,dayl} >= 8'h28) ) )
									begin
										{dayh,dayl} <= 1;
										monl <= monl + 1;
										if (monl == 9)
										begin
											monl <= 0;
											monh <= monh + 1;
										end
										if ({monh, monl} >= 12)
										begin
											{monh, monl} <= 1;
											y0 <= y0 + 1;
											if (y0 >= 9)
											begin
												y0 <= 0;
												y1 <= y1 + 1;
												if (y1 >= 9)
												begin
													y1 <= 0;
													y2 <= y2 + 1;
													if (y2 >= 9)
													begin
														y2 <= 0;
														y3 <= y3 + 1;
														if (y3 >= 9)
															y3 <= 0;
													end
												end
											end
										end
									end
								end
							end
						end
					end
				end
			end
		end
	end


leapyear leap(isleap, y3, y2, y1, y0);
divider div10ms(clk50m, reset, tick10ms);
assign yearh = {y3, y2};
assign yearl = {y1, y0};
assign month = {monh, monl};
assign day = {dayh, dayl};
assign hour = {hourh, hourl};
assign min = {minh, minl};
assign sec = {sech, secl};
assign csec = {csech, csecl};

endmodule
