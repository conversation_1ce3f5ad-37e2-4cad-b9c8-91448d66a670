`timescale 1ns / 1ns

////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer:
//
// Create Date:   15:47:09 09/28/2011
// Design Name:   rtc
// Module Name:   F:/FPGA/RTC/rtc_tst.v
// Project Name:  RTC
// Target Device:  
// Tool versions:  
// Description: 
//
// Verilog Test Fixture created by ISE for module: rtc
//
// Dependencies:
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
////////////////////////////////////////////////////////////////////////////////

module rtc_tst;

	// Inputs
	reg clk50m;
	reg reset;

	// Outputs
	wire [7:0] yearh;
	wire [7:0] yearl;
	wire [7:0] month;
	wire [7:0] day;
	wire [7:0] hour;
	wire [7:0] min;
	wire [7:0] sec;
	wire [7:0] csec;
	wire [7:0] week;

	// Instantiate the Unit Under Test (UUT)
	rtc uut (
		.clk50m(clk50m), 
		.reset(reset), 
		.yearh(yearh), 
		.yearl(yearl), 
		.month(month), 
		.day(day), 
		.hour(hour), 
		.min(min), 
		.sec(sec), 
		.csec(csec), 
		.week(week)
	);

	initial begin
		// Initialize Inputs
		clk50m = 0;
		reset = 1;

		// Wait 100 ns for global reset to finish
		#100;			//FPGA GSR will internally hold for 100nS, then the reset for module will work
		
		// Add stimulus here
		#200	reset = 0;		//delay 200 nS before assert reset to module under test.

	end
      
		
	always #10 clk50m = !clk50m;	
	
endmodule

