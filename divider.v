`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    00:36:43 09/27/2011 
// Design Name: 
// Module Name:    divider 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module divider(clk50m, reset, tick10ms);
input clk50m, reset;
output tick10ms;

reg tick10ms;
reg [11:0] divider;		//at least 11bit to count 500K for 50MHz->100Hz

parameter DIVto10ms = 5;		//50MHz * 0.01s = 500K count; 
                            //set to small for simulation, set 500K for implementation

always @(posedge clk50m)
begin
	if (reset)
		begin
			tick10ms <= 0;
			divider <= 0;
		end
	else
		begin
			divider <= divider + 1;    //block and non-block assignment should be different
			if (divider >= (DIVto10ms - 1))
			   begin
			     divider <= 0;
				   tick10ms <= 1;
				 end
			else
				tick10ms <= 0;          //10mS tick only stay at '1' for 1 cylcle of sys_clk(50MHz)
		end
end

endmodule
