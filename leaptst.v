`timescale 1ns / 1ps

////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer:
//
// Create Date:   15:47:29 09/28/2011
// Design Name:   leapyear
// Module Name:   F:/FPGA/RTC/leaptst.v
// Project Name:  RTC
// Target Device:  
// Tool versions:  
// Description: 
//
// Verilog Test Fixture created by ISE for module: leapyear
//
// Dependencies:
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
////////////////////////////////////////////////////////////////////////////////

module leaptst;

	// Inputs
	reg [3:0] y3;
	reg [3:0] y2;
	reg [3:0] y1;
	reg [3:0] y0;

	// Outputs
	wire isleap;

	// Instantiate the Unit Under Test (UUT)
	leapyear uut (
		.isleap(isleap), 
		.y3(y3), 
		.y2(y2), 
		.y1(y1), 
		.y0(y0)
	);

	initial begin
		// Initialize Inputs
		y3 = 0;
		y2 = 0;
		y1 = 0;
		y0 = 0;

		// Wait 100 ns for global reset to finish
		#100;
        
		// Add stimulus here
		#100	y3 = 2;
				y2 = 0;
				y1 = 0;
				y0 = 0;
				
		#100	y3 = 2;
				y2 = 1;
				y1 = 0;
				y0 = 0;

		#100	y3 = 1;
				y2 = 9;
				y1 = 9;
				y0 = 6;

		#100	y3 = 2;
				y2 = 0;
				y1 = 1;
				y0 = 1;

	end
      
endmodule

